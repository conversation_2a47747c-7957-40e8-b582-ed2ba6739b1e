package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

func main() {
	// 23.116672,113.375473
	asiaJakarta, _ := time.LoadLocation("Asia/Shanghai")
	jakartaSchedules, _ := prayer.Calculate(prayer.Config{
		Latitude:           23.116672,
		Longitude:          113.375473,
		Timezone:           asiaJakarta,
		TwilightConvention: prayer.Diyanet(),
		AsrConvention:      prayer.Han<PERSON>i,
		PreciseToSeconds:   false,
	}, 2025)

	for _, schedule := range jakartaSchedules {
		date := schedule.Date
		if strings.Contains(date, "2025-07-31") {
			fmt.Printf("%s = %v\n", date, schedule)
		}
	}
}
